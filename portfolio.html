<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e1e1e, #2d2d2d);
            color: #e0e0e0;
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            background: rgba(0, 0, 0, 0.8);
            padding: 20px 0;
            position: sticky;
            top: 0;
            z-index: 100;
            border-bottom: 2px solid #4a90e2;
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #4a90e2;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-links a {
            color: #e0e0e0;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            transition: all 0.3s;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background: #4a90e2;
            color: white;
        }

        .page {
            display: none;
            animation: fadeIn 0.5s;
            will-change: opacity, transform;
        }

        .page.active {
            display: block;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Home Page Styles */
        .hero {
            text-align: center;
            padding: 100px 0;
            background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%234a90e2" stroke-width="0.5" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        }

        .hero h1 {
            font-size: 48px;
            margin-bottom: 20px;
            color: #4a90e2;
        }

        .hero p {
            font-size: 20px;
            margin-bottom: 30px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .contact-info {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
            margin-top: 40px;
        }

        .contact-item {
            background: rgba(74, 144, 226, 0.1);
            padding: 15px 25px;
            border-radius: 10px;
            border: 1px solid #4a90e2;
        }

        .contact-item a {
            color: #4a90e2;
            text-decoration: none;
        }

        /* Profile Page Styles */
        .section {
            margin: 40px 0;
            padding: 30px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            border-left: 4px solid #4a90e2;
        }

        .section h2 {
            color: #4a90e2;
            margin-bottom: 20px;
            font-size: 28px;
        }

        .section h3 {
            color: #ffffff;
            margin-bottom: 15px;
            font-size: 22px;
        }

        .experience-item,
        .education-item {
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(74, 144, 226, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(74, 144, 226, 0.2);
        }

        .job-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }

        .job-title {
            font-weight: bold;
            color: #4a90e2;
            font-size: 18px;
        }

        .job-date {
            color: #b0b0b0;
            font-style: italic;
        }

        .company {
            color: #ffffff;
            margin-bottom: 10px;
        }

        ul {
            margin-left: 20px;
            margin-top: 10px;
        }

        li {
            margin-bottom: 8px;
        }

        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .skill-category {
            background: rgba(74, 144, 226, 0.1);
            padding: 20px;
            border-radius: 8px;
            border: 1px solid rgba(74, 144, 226, 0.3);
        }

        .skill-category h4 {
            color: #4a90e2;
            margin-bottom: 10px;
        }

        /* Projects Page Styles */
        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-top: 20px;
        }

        .project-card {
            background: rgba(0, 0, 0, 0.4);
            border-radius: 10px;
            padding: 25px;
            border: 1px solid rgba(74, 144, 226, 0.3);
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .project-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(74, 144, 226, 0.2);
            border-color: #4a90e2;
        }

        .project-title {
            font-family: 'Montserrat', sans-serif;
            color: #4a90e2;
            font-size: 22px;
            margin-bottom: 15px;
            font-weight: 600;
            letter-spacing: -0.3px;
        }

        .project-tech {
            color: #b0b0b0;
            font-style: italic;
            margin-bottom: 15px;
            font-size: 14px;
        }

        .project-description {
            margin-bottom: 15px;
        }

        .project-features {
            margin-top: 15px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 36px;
            }

            .hero p {
                font-size: 18px;
            }

            .contact-info {
                gap: 15px;
            }

            .contact-item {
                padding: 10px 15px;
            }

            .nav-links {
                gap: 15px;
            }

            .nav-links a {
                padding: 8px 15px;
                font-size: 14px;
            }

            .job-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .projects-grid {
                grid-template-columns: 1fr;
            }
        }

        footer {
            text-align: center;
            padding: 40px 0;
            margin-top: 60px;
            border-top: 1px solid rgba(74, 144, 226, 0.3);
            color: #b0b0b0;
        }
    </style>
</head>

<body>
    <header>
        <nav>
            <div class="logo">MQK</div>
            <ul class="nav-links">
                <li><a href="#home" class="nav-link active" onclick="showPage('home', this)">Home</a></li>
                <li><a href="#profile" class="nav-link" onclick="showPage('profile', this)">Profile</a></li>
                <li><a href="#projects" class="nav-link" onclick="showPage('projects', this)">Projects</a></li>
            </ul>
        </nav>
    </header>

    <!-- Home Page -->
    <div id="home" class="page active">
        <div class="container">
            <div class="hero">
                <h1>Muhammad Qasim Khan</h1>
                <p>Computer Science Student | AI/ML Engineer | Full-Stack Developer</p>
                <p>Passionate about Computer Vision, Machine Learning, and building innovative solutions</p>

                <div class="contact-info">
                    <div class="contact-item">
                        <strong>Phone:</strong> <a href="tel:+923337239239">+92-333-7239239</a>
                    </div>
                    <div class="contact-item">
                        <strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a>
                    </div>
                    <div class="contact-item">
                        <strong>LinkedIn:</strong> <a href="https://www.linkedin.com/in/muhammad-qasim-khan-14a587189/"
                            target="_blank" rel="noopener">Muhammad Qasim Khan</a>
                    </div>
                    <div class="contact-item">
                        <strong>GitHub:</strong> <a href="https://github.com/qasimkhan628" target="_blank"
                            rel="noopener">qasimkhan628</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Page -->
    <div id="profile" class="page">
        <div class="container">
            <div class="section">
                <h2>Education</h2>
                <div class="education-item">
                    <div class="job-header">
                        <div>
                            <div class="job-title">BSc. in Computer Science</div>
                            <div class="company">Habib University, Karachi, Sindh, Pakistan</div>
                        </div>
                        <div class="job-date">Dec 2025</div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>Professional Experience</h2>

                <div class="experience-item">
                    <div class="job-header">
                        <div>
                            <div class="job-title">Computer Vision Intern</div>
                            <div class="company">Indus Motor Company (Toyota) - Karachi (Remote)</div>
                        </div>
                        <div class="job-date">Nov 2024 – May 2025</div>
                    </div>
                    <ul>
                        <li>Built a detection system to identify mobile phone usage violations using YOLOv8, Roboflow,
                            and Gemini 2.0 Flash improving accuracy to 98%</li>
                        <li>Trained models and deployed on Toyota's internal server for real-time evaluation</li>
                        <li>Created full-stack frontend using React 19, Vite, Express.js for video upload, detection,
                            and metric visualization</li>
                        <li>Wrote deployment guides and technical documentation for end-users</li>
                    </ul>
                </div>

                <div class="experience-item">
                    <div class="job-header">
                        <div>
                            <div class="job-title">Full-Stack Fellow</div>
                            <div class="company">Headstarter Fellowship - Karachi (Remote)</div>
                        </div>
                        <div class="job-date">June 2024 – Aug 2024</div>
                    </div>
                    <ul>
                        <li>Built five AI-enabled full-stack applications using React, Node.js, and modern LLM/ML APIs
                        </li>
                        <li>Attended weekly code reviews, career mentorship sessions, and a final demo day</li>
                        <li>Collaborated with other fellows on rapid prototyping and integration</li>
                    </ul>
                </div>

                <div class="experience-item">
                    <div class="job-header">
                        <div>
                            <div class="job-title">Data Science and Machine Learning Apprentice</div>
                            <div class="company">ConnectHear - Karachi (Remote)</div>
                        </div>
                        <div class="job-date">July 2023 – Sep 2023</div>
                    </div>
                    <ul>
                        <li>Analyzed sign language vector embeddings using PCA for classification overlap visualization
                        </li>
                        <li>Implemented custom evaluation metrics including F1, Precision, Recall, and confusion matrix
                        </li>
                    </ul>
                </div>

                <div class="experience-item">
                    <div class="job-header">
                        <div>
                            <div class="job-title">Artificial Intelligence Intern</div>
                            <div class="company">Cognizant - Karachi (Remote)</div>
                        </div>
                        <div class="job-date">May 2023 – July 2023</div>
                    </div>
                    <ul>
                        <li>Improved grocery sales forecasting model accuracy through feature engineering and
                            multi-source dataset merging for Gala Groceries</li>
                        <li>Conducted exploratory data analysis to guide modeling and strategic recommendations,
                            achieving 80% accuracy</li>
                    </ul>
                </div>
            </div>

            <div class="section">
                <h2>Technical Skills</h2>
                <div class="skills-grid">
                    <div class="skill-category">
                        <h4>Programming Languages</h4>
                        <p>Python, JavaScript, TypeScript, HTML/CSS, SQL</p>
                    </div>
                    <div class="skill-category">
                        <h4>Frameworks & Libraries</h4>
                        <p>React, Node.js, Express, Vite, Flask, TensorFlow, Keras</p>
                    </div>
                    <div class="skill-category">
                        <h4>Developer Tools</h4>
                        <p>VS Code, Cursor, Trae, Kiro, Git/GitHub, Jupyter Notebook</p>
                    </div>
                    <div class="skill-category">
                        <h4>AI/ML Libraries</h4>
                        <p>OpenCV, YOLOv8/YOLOv11, Gemini 2.0, Roboflow Supervision, Pandas, Scikit-learn, EasyOCR,
                            Matplotlib, NumPy</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Projects Page -->
    <div id="projects" class="page">
        <div class="container">
            <div class="section">
                <h2>Featured Projects</h2>

                <div class="projects-grid">
                    <div class="project-card">
                        <h3 class="project-title">Urdu Sentiment Classification Using LSTM</h3>
                        <div class="project-tech">TensorFlow, Keras, Matplotlib, Pandas</div>
                        <div class="project-description">
                            Built a sentiment classifier to categorize Urdu news headlines about government COVID-19
                            actions.
                        </div>
                        <div class="project-features">
                            <strong>Key Features:</strong>
                            <ul>
                                <li>Trained on 1,000 Kaggle samples and tested on 200 manually collected headlines</li>
                                <li>Achieved 100% training accuracy with BiLSTM model</li>
                                <li>Implemented in Jupyter Notebook</li>
                            </ul>
                        </div>
                        <div class="project-tech">Dec 2021 – Jan 2022</div>
                    </div>

                    <div class="project-card">
                        <h3 class="project-title">License Plate Detection Using EasyOCR</h3>
                        <div class="project-tech">Pandas, Scikit-learn, Jupyter, OpenCV, EasyOCR</div>
                        <div class="project-description">
                            Built a basic vehicle license plate detector using OCR and image preprocessing techniques.
                        </div>
                        <div class="project-features">
                            <strong>Key Features:</strong>
                            <ul>
                                <li>Implemented OCR and image preprocessing in Jupyter Notebook</li>
                                <li>Tested system on various car images for accuracy and plate localization</li>
                                <li>Used OpenCV for image processing and EasyOCR for text recognition</li>
                            </ul>
                        </div>
                        <div class="project-tech">March 2023</div>
                    </div>

                    <div class="project-card">
                        <h3 class="project-title">Credit Card Fraud Detection with Random Forest</h3>
                        <div class="project-tech">Pandas, Scikit-learn, Jupyter</div>
                        <div class="project-description">
                            Evaluated transaction patterns to detect fraudulent activity using machine learning
                            algorithms.
                        </div>
                        <div class="project-features">
                            <strong>Key Features:</strong>
                            <ul>
                                <li>Used Random Forest Classifier for fraud detection</li>
                                <li>Performed extensive EDA and handled class imbalance</li>
                                <li>Visualized model performance and feature importance</li>
                            </ul>
                        </div>
                        <div class="project-tech">May 2023</div>
                    </div>

                    <div class="project-card">
                        <h3 class="project-title">Mobile Phone Detection System (Toyota)</h3>
                        <div class="project-tech">YOLOv8, Roboflow, Gemini 2.0, React 19, Express.js</div>
                        <div class="project-description">
                            Professional computer vision system for detecting mobile phone usage violations in
                            automotive environments.
                        </div>
                        <div class="project-features">
                            <strong>Key Features:</strong>
                            <ul>
                                <li>98% accuracy in mobile phone detection</li>
                                <li>Real-time deployment on Toyota's internal servers</li>
                                <li>Full-stack web interface for video upload and analysis</li>
                                <li>Comprehensive documentation and deployment guides</li>
                            </ul>
                        </div>
                        <div class="project-tech">Nov 2024 – May 2025</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <div class="container">
            <p>&copy; 2025 Muhammad Qasim Khan. All rights reserved.</p>
            <p>Computer Science Student | AI/ML Engineer | Full-Stack Developer</p>
        </div>
    </footer>

    <script>
        function showPage(pageId, linkElement) {
            // Hide all pages
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => page.classList.remove('active'));

            // Show selected page
            document.getElementById(pageId).classList.add('active');

            // Update active link
            const links = document.querySelectorAll('.nav-link');
            links.forEach(link => link.classList.remove('active'));
            linkElement.classList.add('active');
        }
    </script>
</body>

</html>